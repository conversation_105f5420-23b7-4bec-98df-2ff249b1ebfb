package handler

import (
	"digital-transformation-api/infrastructure"
	"digital-transformation-api/internal/line/port/staff-db"
	"digital-transformation-api/internal/line/service/staff"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type staffHandler struct {
	service staff.Service
}

func NewStaffHandler(service staff.Service) *staffHandler {
	return &staffHandler{service: service}
}

func (h *staffHandler) Handle(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	if rctx.Header.TokenLine == "" {
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	resp, err := h.service.Execute(&staff.Request{}, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, resp)
}

func BindStaffRoute(app gins.GinApps) {
	svc := staff.New(
		staffdb.NewAdaptorPG(infrastructure.Db),
	)
	hdl := NewStaffHandler(svc)
	app.Register(
		http.MethodGet,
		"/line/staff/get-info",
		app.ParseRouteContext(hdl.Handle),
	)
} 