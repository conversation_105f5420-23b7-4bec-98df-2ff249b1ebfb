package staff

import (
	"digital-transformation-api/internal/line/port/staff-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	callDb staffdb.Port
}

func New(callDb staffdb.Port) Service {
	return &service{callDb: callDb}
}

func (s *service) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	if rctx == nil || rctx.Header.TokenLine == "" {
		return nil, errs.NewBadRequestError()
	}

	dbResp, err := s.callDb.Execute(&staffdb.Request{LineToken: rctx.Header.TokenLine}, rctx, l)
	if err != nil {
		return nil, err
	}
	if dbResp == nil {
		return &Response{}, nil
	}

	resp := &Response{
		Success: dbResp.Success,
		Data:    dbResp.Data,
		Version: dbResp.Version,
	}
	return resp, nil
}
