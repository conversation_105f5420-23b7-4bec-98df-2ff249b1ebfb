package staffdb

import (
	"digital-transformation-api/internal/common/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Port interface {
	Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error)
}

type Request struct {
	LineToken string `validate:"required"`
}

type Response struct {
	Success bool         `json:"success"`
	Data    domain.Staff `json:"data"`
	Version string       `json:"version"`
}
