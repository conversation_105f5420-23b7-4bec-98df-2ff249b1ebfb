package staffdb

import (
	"digital-transformation-api/internal/common/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{db: db}
}

func (a *adaptorPG) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	l = logs.NewSpanLog(l)
	lineToken := rctx.Header.TokenLine
	l.Info(lineToken)
	if lineToken == "" {
		l.<PERSON>rf("missing userId header")
		return nil, errs.NewCustom(400, "400011111", "Missing userId header", "Missing userId header")
	}

	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Table("staff").Where("line_token = ?", lineToken).Take(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errs.NewCustom(500, "400022222", "Staff not found", "Staff not found")
		}
		l.Errorf("query staff by line_token error: %v", err)
		return nil, errs.NewInternalError()
	}

	return &Response{Success: true, Data: staff, Version: "1.0.0"}, nil
}
