package domain

import (
	"time"
	"github.com/lib/pq"
)

// Staff represents an employee/staff member.
// Primary key: ID
// Foreign keys: ShopID -> shop.id, AreaID (external), SupervisorID -> staff.id, RoleID -> role.id
type Staff struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// ShopID references the shop
	ShopID *int64 `json:"shop_id,omitempty" db:"shop_id"`
	// AreaID references an area
	AreaID *int64 `json:"area_id,omitempty" db:"area_id"`
	// SupervisorID references the supervisor staff
	SupervisorID *int64 `json:"supervisor_id,omitempty" db:"supervisor_id"`
	// RoleID references the role
	RoleID *int64 `json:"role_id,omitempty" db:"role_id"`
	// Permissions is a list of permissions granted to the staff
	Permissions pq.StringArray `json:"permissions,omitempty" db:"permissions" gorm:"type:varchar[]"`
	// EmployeeID is the internal employee identifier
	EmployeeID *string `json:"employee_id,omitempty" db:"employee_id"`
	// Firstname is the staff first name
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the staff last name
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the staff email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// LineToken is the LINE token for messaging
	LineToken *string `json:"line_token,omitempty" db:"line_token"`
	// Phone is the staff phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// Gender is the staff gender
	Gender *string `json:"gender,omitempty" db:"gender"`
	// Education is the staff education level
	Education *string `json:"education,omitempty" db:"education"`
	// DateOfBirth is the staff date of birth
	DateOfBirth *time.Time `json:"date_of_birth,omitempty" db:"date_of_birth"`
	// ProfileURL is the URL of the staff profile image
	ProfileURL *string `json:"profile_url,omitempty" db:"profile_url"`
	// ProfileURLContentType is the content type of the profile URL
	ProfileURLContentType *string `json:"profile_url_content_type,omitempty" db:"profile_url_content_type"`
	// ReferenceCode is a referral or reference code
	ReferenceCode *string `json:"reference_code,omitempty" db:"reference_code"`
	// Status indicates the current status of the staff
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 